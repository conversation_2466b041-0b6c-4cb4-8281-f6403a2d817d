import request from "@/utils/request";

/**
 * 分页查询服务商账单信息
 * 支持多种筛选条件的组合查询，包括站点编号、站点名称、省市、负责人、结算类型、账单周期、结算进度等
 * @param {Object} data - 查询参数
 * @param {number} [data.pageNum=1] - 页码
 * @param {number} [data.pageSize=10] - 每页条数
 * @param {number} [data.tenantId] - 租户号
 * @param {number} [data.orgNo] - 组织编号
 * @param {Array<number>} [data.orgNoList] - 组织编号列表
 * @param {number} [data.operatorId] - 操作员ID
 * @param {string} [data.operatorName] - 操作员名称
 * @param {string} [data.stationCode] - 站点编号（模糊查询）
 * @param {string} [data.stationName] - 站点名称（模糊查询）
 * @param {string} [data.province] - 省份编码
 * @param {string} [data.city] - 市编码
 * @param {number} [data.manager] - 负责人ID
 * @param {string} [data.managerName] - 负责人名称（模糊查询）
 * @param {string} [data.settlementType] - 结算类型（字典：settlement_type）
 * @param {string} [data.billStartDate] - 账单开始日期（格式：YYYY-MM-DD）
 * @param {string} [data.billEndDate] - 账单截止日期（格式：YYYY-MM-DD）
 * @param {string} [data.settleProgress] - 结算进度（字典：settle_progress）
 * @returns {Promise<Object>} 返回分页查询结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {Array<Object>} returns.data - 服务商账单信息列表
 * @returns {number} returns.data[].id - 主键ID
 * @returns {string} returns.data[].stationCode - 站点编号
 * @returns {string} returns.data[].stationName - 站点名称
 * @returns {string} returns.data[].province - 省份编码
 * @returns {string} returns.data[].provinceName - 省份名称
 * @returns {string} returns.data[].city - 市编码
 * @returns {string} returns.data[].cityName - 市名称
 * @returns {number} returns.data[].manager - 负责人ID
 * @returns {string} returns.data[].managerName - 负责人名称
 * @returns {string} returns.data[].settlementType - 结算类型编码（字典：settlement_type）
 * @returns {string} returns.data[].settlementTypeName - 结算类型名称
 * @returns {string} returns.data[].billStartDate - 账单开始日期
 * @returns {string} returns.data[].billEndDate - 账单截止日期
 * @returns {string} returns.data[].billCycle - 账单周期显示文本
 * @returns {number} returns.data[].serviceFeeAmount - 服务费应结算金额（元）
 * @returns {number} returns.data[].serviceFeeStandard - 服务费计算标准（%）
 * @returns {number} returns.data[].serviceFeeRealAmount - 服务费实际结算金额（元）
 * @returns {string} returns.data[].settleProgress - 结算进度编码（字典：settle_progress）
 * @returns {string} returns.data[].settleProgressName - 结算进度名称
 * @returns {string} returns.data[].settleRemark - 结算备注
 * @returns {string} returns.data[].serviceFeeSettleBy - 服务费结算人员
 * @returns {number} returns.data[].tenantId - 租户号
 * @returns {number} returns.data[].orgNo - 组织编号
 * @returns {string} returns.data[].orgNoName - 组织名称
 * @returns {number} returns.data[].creator - 创建人ID
 * @returns {string} returns.data[].creatorName - 创建人名称
 * @returns {string} returns.data[].createTime - 创建时间
 * @returns {string} returns.data[].updateTime - 更新时间
 * @returns {string} returns.data[].belongPlace - 所属地区
 * @returns {number} returns.pageNum - 当前页码
 * @returns {number} returns.pageSize - 每页条数
 * @returns {number} returns.total - 总记录数
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 查询第一页数据
 * const result = await serviceProviderBillApi.queryList({
 *   pageNum: 1,
 *   pageSize: 10,
 *   stationCode: 'ST001'
 * });
 */
export function queryList(data) {
  return request({
    url: "/st/serviceProviderBill/queryList",
    method: "post",
    data,
  });
}

/**
 * 删除服务商账单信息
 * @param {Object} params - 删除参数
 * @param {number} params.id - 要删除的服务商账单信息ID（必填）
 * @returns {Promise<Object>} 返回删除结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {null} returns.data - 删除结果数据（通常为null）
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 删除服务商账单信息
 * const result = await serviceProviderBillApi.remove({ id: 1 });
 */
export function remove(params) {
  return request({
    url: "/st/serviceProviderBill/remove",
    method: "get",
    params,
  });
}

/**
 * 导出服务商账单信息Excel文件
 * @param {Object} data - 导出参数（与查询参数相同，用于筛选导出数据）
 * @param {number} [data.pageNum] - 页码
 * @param {number} [data.pageSize] - 每页条数
 * @param {number} [data.tenantId] - 租户号
 * @param {number} [data.orgNo] - 组织编号
 * @param {Array<number>} [data.orgNoList] - 组织编号列表
 * @param {number} [data.operatorId] - 操作员ID
 * @param {string} [data.operatorName] - 操作员名称
 * @param {string} [data.stationCode] - 站点编号
 * @param {string} [data.stationName] - 站点名称
 * @param {string} [data.province] - 省份编码
 * @param {string} [data.city] - 市编码
 * @param {number} [data.manager] - 负责人ID
 * @param {string} [data.managerName] - 负责人名称
 * @param {string} [data.settlementType] - 结算类型
 * @param {string} [data.billStartDate] - 账单开始日期
 * @param {string} [data.billEndDate] - 账单截止日期
 * @param {string} [data.settleProgress] - 结算进度
 * @returns {Promise<Object>} 返回导出结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {string} returns.data - 导出文件下载链接或文件内容
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 导出所有服务商账单信息
 * const result = await serviceProviderBillApi.exportData({
 *   stationCode: 'ST001'
 * });
 */
export function exportData(data) {
  return request({
    url: "/st/serviceProviderBill/export",
    method: "post",
    data,
  });
}

/**
 * 新增/编辑服务商账单信息
 * @param {Object} data - 服务商账单信息数据
 * @param {number} [data.id] - 主键ID（编辑时必填）
 * @param {string} data.stationCode - 站点编号（必填）
 * @param {string} data.stationName - 站点名称（必填）
 * @param {string} [data.province] - 省份编码
 * @param {string} [data.provinceName] - 省份名称
 * @param {string} [data.city] - 市编码
 * @param {string} [data.cityName] - 市名称
 * @param {number} [data.manager] - 负责人ID
 * @param {string} [data.managerName] - 负责人名称
 * @param {string} data.settlementType - 结算类型（必填，字典：settlement_type）
 * @param {string} [data.settlementTypeName] - 结算类型名称
 * @param {string} data.billStartDate - 账单开始日期（必填）
 * @param {string} data.billEndDate - 账单截止日期（必填）
 * @param {string} [data.billCycle] - 账单周期显示文本
 * @param {number} [data.serviceFeeAmount] - 服务费应结算金额（元）
 * @param {number} [data.serviceFeeStandard] - 服务费计算标准（%）
 * @param {number} [data.serviceFeeRealAmount] - 服务费实际结算金额（元）
 * @param {string} [data.settleProgress] - 结算进度（字典：settle_progress）
 * @param {string} [data.settleProgressName] - 结算进度名称
 * @param {string} [data.settleRemark] - 结算备注
 * @param {string} [data.serviceFeeSettleBy] - 服务费结算人员
 * @param {number} [data.tenantId] - 租户号
 * @param {number} [data.orgNo] - 组织编号
 * @param {string} [data.orgNoName] - 组织名称
 * @param {number} [data.creator] - 创建人ID
 * @param {string} [data.creatorName] - 创建人名称
 * @param {string} [data.createTime] - 创建时间
 * @param {string} [data.updateTime] - 更新时间
 * @param {string} [data.belongPlace] - 所属地区
 * @param {number} [data.operatorId] - 操作员ID
 * @param {string} [data.operatorName] - 操作员名称
 * @returns {Promise<Object>} 返回新增/编辑结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {null} returns.data - 操作结果数据（通常为null）
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 新增服务商账单信息
 * const result = await serviceProviderBillApi.update({
 *   stationCode: 'ST001',
 *   stationName: '测试站点',
 *   settlementType: 'type_001',
 *   billStartDate: '2024-01-01',
 *   billEndDate: '2024-01-31'
 * });
 */
export function update(data) {
  return request({
    url: "/st/serviceProviderBill/save",
    method: "post",
    data,
  });
}

/**
 * 获取服务费结算人员列表
 * 用于获取服务费结算人员下拉选择器的数据源
 * @param {Object} params - 查询参数
 * @param {string} [params.name] - 人员姓名（模糊查询）
 * @returns {Promise<Object>} 返回服务费结算人员列表查询结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {Array<string>} returns.data - 服务费结算人员名称列表
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 获取服务费结算人员列表
 * const result = await serviceProviderBillApi.settlePersonList({
 *   name: '张'
 * });
 */
export function settlePersonList(params) {
  return request({
    url: "/st/serviceProviderBill/settlePersonList",
    method: "get",
    params,
  });
}
/**
 * 分页查询充电平台的站点信息
 * 用于站点选择器的数据源，支持按站点编码和站点名称模糊搜索
 * @param {Object} data - 查询参数
 * @param {number} [data.pageNum=1] - 页码
 * @param {number} [data.pageSize=10] - 每页条数
 * @param {number} [data.tenantId] - 租户ID
 * @param {number} [data.orgNo] - 机构编号
 * @param {Array<number>} [data.orgNoList] - 机构编号列表
 * @param {number} [data.operatorId] - 运营商ID
 * @param {string} [data.operatorName] - 运营商名称
 * @param {string} [data.stationNo] - 站点编码（模糊查询）
 * @param {string} [data.stationName] - 站点名称（模糊查询）
 * @returns {Promise<Object>} 返回站点信息查询结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {Array<Object>} returns.data - 站点信息列表
 * @returns {string} returns.data[].stationId - 站点ID
 * @returns {string} returns.data[].stationNo - 站点编码（运营商自编的编号）
 * @returns {string} returns.data[].stationName - 站点名称
 * @returns {string} returns.data[].stationType - 站点类型
 * @returns {number} returns.data[].pileNum - 站点充电桩个数
 * @returns {string} returns.data[].province - 省
 * @returns {string} returns.data[].city - 城市
 * @returns {string} returns.data[].county - 区/县
 * @returns {string} returns.data[].stationAddress - 地址：区下面的详细地址
 * @returns {string} returns.data[].operationStatus - 运营状态
 *   - '01': 建设中
 *   - '02': 运营
 *   - '03': 停运
 *   - '04': 检修
 *   - '05': 退运
 *   - '06': 已删除
 * @returns {string} returns.data[].buildDate - 建设时间
 * @returns {string} returns.data[].openDate - 投运时间
 * @returns {string} returns.data[].onlineDate - 上线日期
 * @returns {number} returns.data[].openFlag - 是否对外开放（1是0否）
 * @returns {string} returns.data[].stationChargeType - 站点充电分类
 *   - '01': 交流充电站
 *   - '02': 直流充电桩
 *   - '03': 交直流混合充电站
 * @returns {string} returns.data[].businessTime - 运营时间
 * @returns {string} returns.data[].construction - 建设场所（字典：construction）
 * @returns {number} returns.pageNum - 当前页码
 * @returns {number} returns.pageSize - 每页条数
 * @returns {number} returns.total - 总记录数
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 查询站点信息用于选择器
 * const result = await settleAnalysisApi.queryStationList({
 *   pageNum: 1,
 *   pageSize: 20,
 *   stationName: '测试'
 * });
 */
export function queryStationList(data) {
  return request({
    url: "/st/stationSettlementAnalysis/queryStationList",
    method: "post",
    data,
  });
}

/**
 * 获取负责人列表
 * 用于负责人下拉选择器的数据源，支持按姓名模糊搜索
 * @param {Object} params - 查询参数
 * @param {string} [params.name] - 负责人姓名（模糊查询）
 * @returns {Promise<Object>} 返回负责人列表查询结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {Array<string>} returns.data - 负责人姓名列表
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 获取负责人列表
 * const result = await electricityBillApi.getManagerList({
 *   name: '张'
 * });
 */
export function getManagerList(params) {
  return request({
    url: "/st/serviceProviderBill/managerList",
    method: "get",
    params,
  });
}
// 导出所有方法
export default {
  queryList,
  remove,
  exportData,
  update,
  settlePersonList,
  queryStationList,
  getManagerList,
};
