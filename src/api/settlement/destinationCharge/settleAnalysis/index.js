import request from "@/utils/request";

/**
 * 分页查询场站结算情况分析信息
 * 支持多种筛选条件的组合查询，包括站点编码、站点名称、运营状态等
 * @param {Object} data - 查询参数
 * @param {number} [data.pageNum=1] - 页码
 * @param {number} [data.pageSize=10] - 每页条数
 * @param {number} [data.tenantId] - 租户ID
 * @param {number} [data.orgNo] - 机构编号
 * @param {Array<number>} [data.orgNoList] - 机构编号列表
 * @param {number} [data.operatorId] - 运营商ID
 * @param {string} [data.operatorName] - 运营商名称
 * @param {string} [data.stationCode] - 站点编码（模糊查询）
 * @param {string} [data.stationNo] - 站点编号
 * @param {string} [data.stationName] - 站点名称（模糊查询）
 * @param {string} [data.operationStatus] - 运营状态
 *   - '01': 建设中
 *   - '02': 运营
 *   - '03': 停运
 *   - '04': 检修
 *   - '05': 退运
 *   - '06': 已删除
 * @param {string} [data.manager] - 负责人
 * @param {string} [data.province] - 省份
 * @param {string} [data.city] - 城市
 * @param {string} [data.region] - 所属区域
 * @param {string} [data.onlineStartDate] - 上线开始日期（格式：YYYY-MM-DD）
 * @param {string} [data.onlineEndDate] - 上线结束日期（格式：YYYY-MM-DD）
 * @returns {Promise<Object>} 返回分页查询结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {Array<Object>} returns.data - 场站结算情况分析信息列表
 * @returns {number} returns.data[].id - 主键
 * @returns {string} returns.data[].stationCode - 站点编码
 * @returns {string} returns.data[].stationName - 站点名称
 * @returns {string} returns.data[].stationType - 站点类型
 * @returns {string} returns.data[].construction - 建设场所
 * @returns {string} returns.data[].stationChargeType - 站点充电类型（字典：cm_station_charge_type）
 * @returns {string} returns.data[].operationStatus - 运营状态（字典：cm_station_status）
 * @returns {string} returns.data[].openDate - 投运日期
 * @returns {string} returns.data[].onlineDate - 上线日期
 * @returns {number} returns.data[].pileNum - 充电桩数量
 * @returns {string} returns.data[].settlementCycle - 实际结算周期（字典：settlement_cycle）
 * @returns {string} returns.data[].region - 所属区域
 * @returns {number} returns.data[].manager - 负责人ID
 * @returns {string} returns.data[].managerName - 负责人姓名
 * @returns {string} returns.data[].province - 省份
 * @returns {string} returns.data[].city - 城市
 * @returns {string} returns.data[].settlementType - 结算对象类型（字典：settlement_type）
 * @returns {string} returns.data[].lastMeterTime - 最近抄表时间
 * @returns {number} returns.data[].siteElectricity - 场地方电量（kWh）
 * @returns {number} returns.data[].siteFee - 场地方电费（元）
 * @returns {number} returns.data[].platformCharge - 平台充电电量（kWh）
 * @returns {number} returns.data[].platformFee - 平台充电电费（元）
 * @returns {number} returns.data[].electricalLoss - 电损=（场地方电量-充电电量）/场地方电量
 * @returns {number} returns.data[].feeSettle - 电费结算金额（元）
 * @returns {number} returns.data[].feeDeviation - 电费偏差（%）
 * @returns {string} returns.data[].servicePerson - 服务费结算人员
 * @returns {string} returns.data[].serviceRule - 服务费结算规则
 * @returns {string} returns.data[].serviceStartDate - 服务费初始结算时间
 * @returns {string} returns.data[].serviceLastDate - 服务费最近结算时间
 * @returns {number} returns.data[].serviceSettleAmount - 服务费结算金额
 * @returns {string} returns.data[].remark - 结算特殊情况备注
 * @returns {number} returns.data[].tenantId - 租户ID
 * @returns {number} returns.data[].creator - 创建人ID
 * @returns {string} returns.data[].creatorName - 创建人名称
 * @returns {string} returns.data[].createTime - 创建时间
 * @returns {string} returns.data[].updateTime - 更新时间
 * @returns {number} returns.pageNum - 当前页码
 * @returns {number} returns.pageSize - 每页条数
 * @returns {number} returns.total - 总记录数
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 查询第一页数据
 * const result = await settleAnalysisApi.list({
 *   pageNum: 1,
 *   pageSize: 10,
 *   stationName: '测试站点'
 * });
 */
export function queryList(data) {
  return request({
    url: "/st/stationSettlementAnalysis/queryList",
    method: "post",
    data,
  });
}

/**
 * 分页查询充电平台的站点信息
 * 用于站点选择器的数据源，支持按站点编码和站点名称模糊搜索
 * @param {Object} data - 查询参数
 * @param {number} [data.pageNum=1] - 页码
 * @param {number} [data.pageSize=10] - 每页条数
 * @param {number} [data.tenantId] - 租户ID
 * @param {number} [data.orgNo] - 机构编号
 * @param {Array<number>} [data.orgNoList] - 机构编号列表
 * @param {number} [data.operatorId] - 运营商ID
 * @param {string} [data.operatorName] - 运营商名称
 * @param {string} [data.stationNo] - 站点编码（模糊查询）
 * @param {string} [data.stationName] - 站点名称（模糊查询）
 * @returns {Promise<Object>} 返回站点信息查询结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {Array<Object>} returns.data - 站点信息列表
 * @returns {string} returns.data[].stationId - 站点ID
 * @returns {string} returns.data[].stationNo - 站点编码（运营商自编的编号）
 * @returns {string} returns.data[].stationName - 站点名称
 * @returns {string} returns.data[].stationType - 站点类型
 * @returns {number} returns.data[].pileNum - 站点充电桩个数
 * @returns {string} returns.data[].province - 省
 * @returns {string} returns.data[].city - 城市
 * @returns {string} returns.data[].county - 区/县
 * @returns {string} returns.data[].stationAddress - 地址：区下面的详细地址
 * @returns {string} returns.data[].operationStatus - 运营状态
 *   - '01': 建设中
 *   - '02': 运营
 *   - '03': 停运
 *   - '04': 检修
 *   - '05': 退运
 *   - '06': 已删除
 * @returns {string} returns.data[].buildDate - 建设时间
 * @returns {string} returns.data[].openDate - 投运时间
 * @returns {string} returns.data[].onlineDate - 上线日期
 * @returns {number} returns.data[].openFlag - 是否对外开放（1是0否）
 * @returns {string} returns.data[].stationChargeType - 站点充电分类
 *   - '01': 交流充电站
 *   - '02': 直流充电桩
 *   - '03': 交直流混合充电站
 * @returns {string} returns.data[].businessTime - 运营时间
 * @returns {string} returns.data[].construction - 建设场所（字典：construction）
 * @returns {number} returns.pageNum - 当前页码
 * @returns {number} returns.pageSize - 每页条数
 * @returns {number} returns.total - 总记录数
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 查询站点信息用于选择器
 * const result = await settleAnalysisApi.queryStationList({
 *   pageNum: 1,
 *   pageSize: 20,
 *   stationName: '测试'
 * });
 */
export function queryStationList(data) {
  return request({
    url: "/st/stationSettlementAnalysis/queryStationList",
    method: "post",
    data,
  });
}

/**
 * 新增/编辑场站结算情况分析信息
 * @param {Object} data - 场站结算情况分析信息数据
 * @param {number} [data.id] - 主键（编辑时必填）
 * @param {string} data.stationCode - 站点编码（必填）
 * @param {string} data.stationName - 站点名称（必填）
 * @param {string} [data.stationType] - 站点类型
 * @param {string} [data.construction] - 建设场所
 * @param {string} [data.stationChargeType] - 站点充电类型（字典：cm_station_charge_type）
 * @param {string} [data.operationStatus] - 运营状态（字典：cm_station_status）
 * @param {string} [data.openDate] - 投运日期（格式：YYYY-MM-DD）
 * @param {string} [data.onlineDate] - 上线日期（格式：YYYY-MM-DD）
 * @param {number} [data.pileNum] - 充电桩数量
 * @param {string} [data.settlementCycle] - 实际结算周期（字典：settlement_cycle）
 * @param {string} [data.region] - 所属区域
 * @param {number} [data.manager] - 负责人ID
 * @param {string} [data.managerName] - 负责人姓名
 * @param {string} [data.province] - 省份
 * @param {string} [data.city] - 城市
 * @param {string} [data.settlementType] - 结算对象类型（字典：settlement_type）
 * @param {string} [data.lastMeterTime] - 最近抄表时间
 * @param {number} [data.siteElectricity] - 场地方电量（kWh）
 * @param {number} [data.siteFee] - 场地方电费（元）
 * @param {number} [data.platformCharge] - 平台充电电量（kWh）
 * @param {number} [data.platformFee] - 平台充电电费（元）
 * @param {number} [data.electricalLoss] - 电损=（场地方电量-充电电量）/场地方电量
 * @param {number} [data.feeSettle] - 电费结算金额（元）
 * @param {number} [data.feeDeviation] - 电费偏差（%）
 * @param {string} [data.servicePerson] - 服务费结算人员
 * @param {string} [data.serviceRule] - 服务费结算规则
 * @param {string} [data.serviceStartDate] - 服务费初始结算时间
 * @param {string} [data.serviceLastDate] - 服务费最近结算时间
 * @param {number} [data.serviceSettleAmount] - 服务费结算金额
 * @param {string} [data.remark] - 结算特殊情况备注
 * @param {number} [data.tenantId] - 租户ID
 * @param {number} [data.creator] - 创建人ID
 * @param {string} [data.creatorName] - 创建人名称
 * @param {number} [data.operatorId] - 运营商ID
 * @param {string} [data.operatorName] - 运营商名称
 * @param {string} [data.stationNo] - 站点编号
 * @returns {Promise<Object>} 返回保存结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {string} returns.data - 操作结果信息
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 新增场站结算情况分析信息
 * const result = await settleAnalysisApi.update({
 *   stationCode: 'ST001',
 *   stationName: '测试站点',
 *   stationType: '01',
 *   siteElectricity: 1000.50,
 *   siteFee: 800.00
 * });
 */
export function update(data) {
  return request({
    url: "/st/stationSettlementAnalysis/save",
    method: "post",
    data,
  });
}

/**
 * 删除场站结算情况分析信息
 * @param {Object} params - 删除参数
 * @param {number} params.id - 要删除的记录ID（必填）
 * @returns {Promise<Object>} 返回删除结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {string} returns.data - 删除结果信息
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 删除场站结算情况分析信息
 * const result = await settleAnalysisApi.remove({ id: 1 });
 */
export function remove(params) {
  return request({
    url: "/st/stationSettlementAnalysis/remove",
    method: "get",
    params,
  });
}

/**
 * 导出场站结算情况分析信息Excel文件
 * @param {Object} data - 导出参数（与查询参数相同，用于筛选导出数据）
 * @param {number} [data.pageNum] - 页码
 * @param {number} [data.pageSize] - 每页条数
 * @param {number} [data.tenantId] - 租户ID
 * @param {number} [data.orgNo] - 机构编号
 * @param {Array<number>} [data.orgNoList] - 机构编号列表
 * @param {number} [data.operatorId] - 运营商ID
 * @param {string} [data.operatorName] - 运营商名称
 * @param {string} [data.stationCode] - 站点编码
 * @param {string} [data.stationNo] - 站点编号
 * @param {string} [data.stationName] - 站点名称
 * @param {string} [data.operationStatus] - 运营状态
 * @param {string} [data.manager] - 负责人
 * @param {string} [data.province] - 省份
 * @param {string} [data.city] - 城市
 * @param {string} [data.region] - 所属区域
 * @param {string} [data.onlineStartDate] - 上线开始日期
 * @param {string} [data.onlineEndDate] - 上线结束日期
 * @returns {Promise<Object>} 返回导出结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {string} returns.data - 导出文件下载链接或文件内容
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 导出所有场站结算情况分析信息
 * const result = await settleAnalysisApi.exportData({
 *   stationName: '测试站点'
 * });
 */
export function exportData(data) {
  return request({
    url: "/st/stationSettlementAnalysis/export",
    method: "post",
    data,
  });
}

/**
 * 获取负责人列表
 * 用于负责人下拉选择器的数据源，支持按姓名模糊搜索
 * @param {Object} params - 查询参数
 * @param {string} [params.name] - 负责人姓名（模糊查询）
 * @returns {Promise<Object>} 返回负责人列表查询结果
 * @returns {boolean} returns.success - 请求是否成功
 * @returns {string} returns.code - 响应状态码
 * @returns {string} returns.message - 响应消息
 * @returns {Array<string>} returns.data - 负责人姓名列表
 * @returns {string} returns.traceId - 请求追踪ID
 * @example
 * // 获取负责人列表
 * const result = await electricityBillApi.getManagerList({
 *   name: '张'
 * });
 */
export function getManagerList(params) {
  return request({
    url: "/st/stationSettlementAnalysis/managerList",
    method: "get",
    params,
  });
}

// 默认导出所有方法
export default {
  queryList,
  queryStationList,
  update,
  remove,
  exportData,
  getManagerList,
};
