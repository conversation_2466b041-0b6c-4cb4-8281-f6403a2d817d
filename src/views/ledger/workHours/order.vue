<!-- 订单制工时 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['workHours:order:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/ledger/workHours/order.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import { listDept } from "@/api/common.js";
import { queryTreeList } from "@/api/ledger/businessType.js";
import { queryDeptOrderTree } from "@/api/ledger/workOrderType.js";
import moment from "moment";
export default {
  name: "ledgerList",
  components: {},
  mixins: [exportMixin],
  data() {
    return {
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          //   slots: {
          //     buttons: "toolbar_buttons",
          //   },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
        spanMethod: this.rowMergeMethod,
      },
      tableData: [],

      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "accept",
      //buse参数-e

      deptOptions: [],
      checkStatusOptions: [],
      workHourTypeOptions: [],
      projectTypeOptions: [],
      orderTypeOptions: [],
      businessTypeOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    this.getDicts("difference_type").then((response) => {
      this.checkStatusOptions = response.data;
    });
    this.getDicts("work_time_type").then((response) => {
      this.workHourTypeOptions = response.data;
    });
    this.getDicts("work_time_type").then((response) => {
      this.projectTypeOptions = response.data;
    });
    queryTreeList({}).then((res) => {
      this.businessTypeOptions = res.data;
    });
    queryDeptOrderTree({}).then((res) => {
      this.orderTypeOptions = res.data?.map((x) => {
        return { ...x, disabled: true };
      });
    });
    this.getTreeselect();
    // this.loadData();
  },
  methods: {
    checkPermission,
    handleJump(row, tab = "person") {
      this.$router.push({
        name: "ledgerDashboard",
        params: { ...row, tab: tab },
      });
    },

    handleExport() {
      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      const { orderTypeArr, deptIds } = this.params;

      // 处理工单类型多选数据
      const orderTypeIds = this.processMultiCascaderData(orderTypeArr);

      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        oneOrderTypeIds: orderTypeIds.level2 || [],
        twoOrderTypeIds: orderTypeIds.level3 || [],
        threeOrderTypeIds: orderTypeIds.level4 || [],
        deptIds: Array.isArray(deptIds) ? deptIds : deptIds ? [deptIds] : [],
        orderMark: "01",
      };

      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "finishTime",
          title: "完成日期",
          startFieldName: "finishStartTime",
          endFieldName: "finishEndTime",
        },
        {
          field: "submitTime",
          title: "提交日期",
          startFieldName: "createStartTime",
          endFieldName: "createEndTime",
        },
        {
          field: "addTime",
          title: "加单时间",
          startFieldName: "startAddTime",
          endFieldName: "endAddTime",
        },
        {
          field: "handleTime",
          title: "节点处理时间",
          startFieldName: "handleStartTime",
          endFieldName: "handleEndTime",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },
    async loadData() {
      const { orderTypeArr, deptIds } = this.params;

      // 处理工单类型多选数据
      const orderTypeIds = this.processMultiCascaderData(orderTypeArr);

      //数据是双向绑定的，所以直接获取data中定义的params即可获取到所有筛选项的值
      let params = {
        ...this.params,
        oneOrderTypeIds: orderTypeIds.level2 || [],
        twoOrderTypeIds: orderTypeIds.level3 || [],
        threeOrderTypeIds: orderTypeIds.level4 || [],
        deptIds: Array.isArray(deptIds) ? deptIds : deptIds ? [deptIds] : [],
        orderMark: "01",
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      console.log("loadData", params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },
    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },
    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },
    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");
      // crudOperationType:accept/remark/cancel/activityType
      const res = await api[crudOperationType](params);
      if (res?.code === "10000") {
        this.$message.success("提交成功");
        this.loadData();
      } else {
        return false;
      }
    },

    getTreeselect() {
      listDept({}).then((response) => {
        this.deptOptions = this.flattenArray(response.data);
      });
    },
    flattenArray(arr) {
      let result = [];
      const flatten = (arr) => {
        arr?.forEach((item) => {
          result.push(item);
          if (item.children) {
            flatten(item.children);
          }
        });
      };
      flatten(arr);
      return result;
    },
    treeChange(arr) {
      return arr.map((item) => {
        if (item.children && item.children.length > 0) {
          this.treeChange(item.children);
        } else {
          delete item.children;
        }
        item.label = item.deptName;
        item.id = item.deptId;
        return item;
      });
    },
    handleRangeTypeChange() {
      this.params.workRangeTime = undefined;
    },
    rowMergeMethod({ row, $rowIndex, column, data }) {
      const fields = [
        "userName",
        "jobNum",
        "operator",
        "deptName",
        "businessTypeStr",
      ];
      if (fields.includes(column.property)) {
        const prevRow = data[$rowIndex - 1];
        let nextRow = data[$rowIndex + 1];
        if (
          prevRow &&
          prevRow.userName === row.userName &&
          prevRow[column.property] === row[column.property]
        ) {
          return { rowspan: 0, colspan: 0 };
        } else {
          let rowspan = 1;
          while (
            nextRow &&
            nextRow.userName === row.userName &&
            nextRow[column.property] === row[column.property]
          ) {
            rowspan++;
            nextRow = data[$rowIndex + rowspan];
          }
          return { rowspan, colspan: 1 };
        }
      }
    },
    // 处理多选级联选择器数据
    processMultiCascaderData(cascaderData) {
      const result = {
        level1: [],
        level2: [],
        level3: [],
        level4: [],
      };

      // 如果数据为空，直接返回空结果
      if (!cascaderData) {
        return result;
      }

      // 处理旧数据格式（单选模式下可能是字符串或数字）
      if (!Array.isArray(cascaderData)) {
        // 如果是字符串或数字，将其视为第一级的选择
        result.level1.push(cascaderData);
        return result;
      }

      // 处理单个数组（非嵌套数组）的情况
      if (
        Array.isArray(cascaderData) &&
        cascaderData.length > 0 &&
        !Array.isArray(cascaderData[0])
      ) {
        // 如果是单个数组（如 [1, 2, 3]），将其视为一个完整的路径
        if (cascaderData.length >= 1) {
          result.level1.push(cascaderData[0]);
        }
        if (cascaderData.length >= 2) {
          result.level2.push(cascaderData[1]);
        }
        if (cascaderData.length >= 3) {
          result.level3.push(cascaderData[2]);
        }
        if (cascaderData.length >= 4) {
          result.level4.push(cascaderData[3]);
        }
        return result;
      }

      // 处理多选数据格式（嵌套数组）
      cascaderData.forEach((path) => {
        if (Array.isArray(path)) {
          // 根据路径长度确定层级
          if (path.length >= 1) {
            result.level1.push(path[0]);
          }
          if (path.length >= 2) {
            result.level2.push(path[1]);
          }
          if (path.length >= 3) {
            result.level3.push(path[2]);
          }
          if (path.length >= 4) {
            result.level4.push(path[3]);
          }
        }
      });

      return result;
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "userName",
          title: "姓名",
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () =>
                      this.handleJump(
                        {
                          userId: row.userId,
                          handleTime: this.params.handleTime,
                        },
                        "person"
                      ),
                  }}
                >
                  {row.userName}
                </el-button>,
              ];
            },
          },
        },
        {
          field: "jobNum",
          title: "工号",
        },
        {
          field: "operator",
          title: "工时来源",
          formatter: (row) => {
            return "维保通订单制";
          },
        },
        {
          field: "deptName",
          title: "部门名称",
        },
        {
          field: "businessTypeStr",
          title: "业务类型",
        },
        {
          field: "orderTypeStr",
          title: "工单类型",
        },
        {
          field: "totalCount",
          title: "数量（个）",
        },
        {
          field: "totalOrderTime",
          title: "工单总工时（h）",
        },
        {
          field: "totalAddTime",
          title: "加单总工时（h）",
        },
        {
          field: "totalTime",
          title: "工时总和（h）",
          slots: {
            default: ({ row }) => {
              return [
                <el-button
                  type="text"
                  size="large"
                  on={{
                    click: () => {
                      let params = {
                        userName: row.userName,
                        handleTime: this.params.handleTime,
                        businessType: [[]],
                        orderTypeArr: [[]],
                        allHandleUsers: [row.userId],
                      };
                      row.oneBusinessTypeId &&
                        (params["businessType"][0][0] = row.oneBusinessTypeId);
                      row.twoBusinessTypeId &&
                        (params["businessType"][0][1] = row.twoBusinessTypeId);
                      row.threeBusinessTypeId &&
                        (params["businessType"][0][2] =
                          row.threeBusinessTypeId);
                      row.supportDept &&
                        (params["orderTypeArr"][0][0] = row.supportDept);
                      row.oneOrderTypeId &&
                        (params["orderTypeArr"][0][1] = row.oneOrderTypeId);
                      row.twoOrderTypeId &&
                        (params["orderTypeArr"][0][2] = row.twoOrderTypeId);
                      row.threeOrderTypeId &&
                        (params["orderTypeArr"][0][3] = row.threeOrderTypeId);
                      this.handleJump(params, "detail");
                    },
                  }}
                >
                  {row.totalTime || 0}
                </el-button>,
              ];
            },
          },
        },
        {
          field: "orderPrice",
          title: "工单价格（元）",
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "jobNum",
            element: "el-input",
            title: "姓名",
            attrs: {
              placeholder: "姓名或工号查询",
            },
          },
          {
            field: "oneBusinessTypeIds",
            title: "业务类型",
            element: "el-select",
            props: {
              multiple: true,
              filterable: true,
              options: this.businessTypeOptions,
              optionValue: "id",
              optionLabel: "typeName",
            },
          },
          {
            field: "deptIds",
            title: "部门名称",
            element: "el-select",
            props: {
              options: this.deptOptions,
              optionLabel: "deptName",
              optionValue: "deptId",
              filterable: true,
              multiple: true,
              collapseTags: true,
              defaultExpandLevel: 1,
            },
          },
          {
            field: "orderTypeArr",
            title: "工单类型",
            element: "el-cascader",
            props: {
              popperClass: "location",
              collapseTags: true,
              props: {
                expandTrigger: "hover",
                checkStrictly: true,
                multiple: true,
                value: "id",
                label: "typeName",
                children: "childrenList",
              },
              filterable: true,
              options: this.orderTypeOptions,
            },
          },
          {
            field: "finishTime",
            title: "完成日期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "submitTime",
            title: "提交日期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "addTime",
            title: "加单时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "handleTime",
            title: "节点处理时间",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
            defaultValue: [
              moment()
                .startOf("month")
                .format("YYYY-MM-DD"),
              moment().format("YYYY-MM-DD"),
            ],
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        // modalFullScreen: true,
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: false,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "50%",
        formConfig: [],
        crudPermission: [],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "110px",
        },
      };
    },
  },
};
</script>

<style></style>
