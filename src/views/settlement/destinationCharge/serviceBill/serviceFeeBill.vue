<!-- 服务费账单 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['serviceBill:serviceFeeBill:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <!-- <el-select
          v-model="selectPage"
          size="mini"
          style="margin-right: 10px;width: 86px;"
        >
          <el-option label="当前页" value="1"></el-option>
          <el-option label="全部页" value="2"></el-option>
        </el-select> -->
        <el-button
          type="primary"
          @click="handleBatchImport"
          v-has-permi="['serviceBill:serviceFeeBill:import']"
          >导入</el-button
        >
        <el-button
          type="primary"
          @click="handleAdd"
          v-has-permi="['serviceBill:serviceFeeBill:add']"
          >新增</el-button
        >
        <!-- <el-button
            type="primary"
            @click="handleBatchAdd"
            v-has-permi="['serviceBill:serviceFeeBill:batchAdd']"
            >批量新增</el-button
          > -->
      </template>
      <template #modalFooter="{ row, crudOperationType }">
        <div v-if="crudOperationType === 'log'"></div>
      </template>
      <template slot="log" slot-scope="{ row, operationType }">
        <Timeline
          :list="recordList"
          operateTypeTitle="operatorTypeName"
          operatorNameTitle="operatorUserName"
          createTimeTitle="operatorTime"
          operateDetailTitle="remark"
        ></Timeline>
      </template>
    </BuseCrud>
    <BatchUpload
      @uploadSuccess="handleQuery"
      ref="batchUpload"
      title="批量导入"
      :uploadApi="uploadObj.api"
      :templateUrl="uploadObj.url"
      :extraData="uploadObj.extraData"
      :maxSize="0.01953"
      maxSizeText="20m"
    >
    </BatchUpload>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/destinationCharge/serviceBill/serviceProviderBill.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";
import Timeline from "@/components/Timeline/index.vue";
import BatchUpload from "@/components/BatchUpload/index.vue";
import { getDeptList } from "@/api/operationWorkOrder/index.js";
import { queryLog, queryCityTree } from "@/api/common.js";
export default {
  name: "serviceFeeBill",
  mixins: [exportMixin],
  components: {
    Timeline,
    BatchUpload,
  },
  data() {
    return {
      workLoading: false,
      uploadObj: {
        api: "/export/report/importServiceProviderBill",
        url: "/charging-maintenance-ui/static/服务费账单导入模板.xlsx",
        extraData: {},
      },
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "update",
      isEdit: false,
      //buse参数-e

      // 下拉选项数据
      statusOptions: [],
      typeOptions: [],
      reconciliationPersonOptions: [],
      recordList: [],

      // 字典数据
      settlementTypeOptions: [], // 结算类型字典
      settleProgressOptions: [], // 结算进度字典
      deptOptionList: [], // 部门列表
      regionData: [], // 省市数据
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };

    // 获取字典数据
    this.getDicts("settlement_type").then((response) => {
      this.settlementTypeOptions = response.data;
    });

    this.getDicts("settle_progress").then((response) => {
      this.settleProgressOptions = response.data;
    });
    this.getDeptList();
    this.getCityRegionData();
  },
  methods: {
    checkPermission,
    //能投大区下拉选项
    getDeptList() {
      getDeptList({}).then((res) => {
        this.deptOptionList = res.data.map((x) => {
          return { ...x, dictValue: x.deptId, dictLabel: x.deptName };
        });
      });
    },
    getCityRegionData() {
      queryCityTree({}).then((res) => {
        this.regionData = this.cleanTree(res.data);
      });
    },
    cleanTree(arr) {
      return arr.map((item) => {
        // 复制当前对象
        const newItem = { ...item };

        // 处理子节点
        if (newItem.children) {
          // 递归处理子节点
          newItem.children = this.cleanTree(newItem.children);

          // 如果当前层级为 3 且子节点为空，删除 children 属性
          if (newItem.children.length === 0) {
            delete newItem.children;
          }
        }

        return newItem;
      });
    },
    handleBatchAdd() {},

    handleBatchImport() {
      this.$refs.batchUpload.open();
    },

    handleExport() {
      const { region } = this.params;
      let params = {
        ...this.params,
      };
      region?.[0] && (params.province = region[0]);
      region?.[1] && (params.city = region[1]);
      this.handleTimeRange(params);
      this.handleCommonExport(api.exportData, params);
    },
    handleLog(row) {
      queryLog({ businessId: row.id }).then((res) => {
        this.recordList = res.data;
      });
      this.$refs.crud.switchModalView(true, "log");
    },
    handleAdd() {
      this.isEdit = false;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "ADD", {
        ...initParams(this.modalConfig.formConfig),
      });
    },
    rowEdit(row) {
      this.isEdit = true;
      this.operationType = "update";
      this.$refs.crud.switchModalView(true, "UPDATE", {
        ...initParams(this.modalConfig.formConfig),
        ...row,
        billDateRange:
          row.billStartDate && row.billEndDate
            ? [row.billStartDate, row.billEndDate]
            : [],
      });
    },
    deleteRowHandler(row) {
      this.$confirm("是否确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then((res) => {
        let params = {
          id: row.id,
        };
        api.remove(params).then((res) => {
          this.$message.success("删除成功");
          this.loadData();
        });
      });
    },
    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "billDateRange",
          title: "账单周期",
          startFieldName: "billStartDate",
          endFieldName: "billEndDate",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0];
          params[x.endFieldName] = params[x.field][1];
          delete params[x.field];
        }
      });
    },

    async loadData() {
      const { region } = this.params;
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      region?.[0] && (params.province = region[0]);
      region?.[1] && (params.city = region[1]);
      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.queryList(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },

    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      return new Promise(async (resolve) => {
        let params = { ...formParams };
        this.handleTimeRange(params);
        console.log(crudOperationType, formParams, "提交");
        // crudOperationType:update
        const res = await api.update(params);
        if (res?.code === "10000") {
          this.$message.success("提交成功");
          this.loadData();
          resolve(true);
        } else {
          resolve(false);
        }
      });
    },

    // 获取站点数据的方法，用于 PageSelector 组件
    async fetchStationData(params, keyName) {
      try {
        const searchParams = {
          pageNum: params.pageNum,
          pageSize: params.pageSize,
          [keyName]: params.searchText,
        };

        const res = await api.queryStationList(searchParams);
        return {
          data: res.data || [],
          total: res.total || 0,
        };
      } catch (error) {
        console.error("获取站点数据失败:", error);
        return {
          data: [],
          total: 0,
        };
      }
    },
    // 站点编号选择变化处理
    handleStationCodeChange({ value, options }) {
      const selectedStation = options.find(
        (item) => item.stationCode === value
      );
      if (selectedStation) {
        // 自动填充站点名称和基础信息
        this.$refs.crud.setFormFields({
          ...selectedStation,
        });
      }
    },

    // 站点名称选择变化处理
    handleStationNameChange({ value, options }) {
      const selectedStation = options.find(
        (item) => item.stationName === value
      );
      if (selectedStation) {
        // 自动填充站点编号和基础信息
        this.$refs.crud.setFormFields({
          ...selectedStation,
        });
      }
    },
    querySearch(queryString, cb, apiName) {
      api[apiName]({
        name: queryString,
      }).then((res) => {
        const result = res.data?.map((x) => {
          return { value: x };
        });
        cb(result);
      });
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "stationCode",
          title: "站点编号",
          width: 120,
        },
        {
          field: "stationName",
          title: "站点名称",
          width: 150,
        },
        {
          field: "provinceName",
          title: "省份",
          width: 120,
        },
        {
          field: "cityName",
          title: "市",
          width: 120,
        },
        {
          field: "orgNoName",
          title: "所属区域",
          width: 120,
        },
        {
          field: "managerName",
          title: "负责人",
          width: 100,
        },
        {
          field: "settlementTypeName",
          title: "结算类型",
          width: 100,
        },
        {
          field: "billCycle",
          title: "账单周期",
          width: 100,
        },
        {
          field: "serviceFeeAmount",
          title: "服务费应结算金额（元）",
          width: 100,
        },
        {
          field: "serviceFeeStandard",
          title: "服务费计算标准（%）",
          width: 100,
        },
        {
          field: "serviceFeeRealAmount",
          title: "服务费实际结算金额（元）",
          width: 150,
        },
        {
          field: "settleProgressName",
          title: "结算进度",
          width: 100,
        },
        {
          field: "settleRemark",
          title: "结算备注",
          width: 100,
        },
        {
          field: "creatorName",
          title: "创建人",
          width: 100,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 160,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "stationCode",
            element: "el-input",
            title: "站点编号",
          },
          {
            field: "orgNo",
            element: "el-select",
            title: "所属区域",
            props: {
              options: this.deptOptionList,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "region",
            title: "省市",
            element: "custom-cascader",
            attrs: {
              collapseTags: true,
              props: {
                checkStrictly: false,
                multiple: false,
                value: "areaCode",
                label: "areaName",
              },
              options: this.regionData, //省市数据,
            },
          },
          {
            field: "billDateRange",
            title: "账单周期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
          },
          {
            field: "stationName",
            element: "page-autocomplete",
            title: "站点名称",
            props: {
              clearable: true,
              optionValue: "stationName",
              optionLabel: "stationName",
              fetchMethod: (params) => {
                return this.fetchStationData(params, "stationName");
              },
            },
          },
          {
            field: "managerName",
            title: "负责人",
            element: "el-autocomplete",
            props: {
              fetchSuggestions: (queryString, cb) => {
                this.querySearch(queryString, cb, "getManagerList");
              },
            },
          },
          {
            field: "settleProgress",
            element: "el-select",
            title: "结算进度",
            props: {
              options: this.settleProgressOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
          {
            field: "settlementType",
            element: "el-select",
            title: "结算类型",
            props: {
              options: this.settlementTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      const form = {
        update: [
          {
            field: "stationCode",
            title: "站点编号",
            element: "page-selector",
            rules: [{ required: true, message: "请选择站点编号" }],
            props: {
              fetchMethod: (params) => {
                return this.fetchStationData(params, "stationCode");
              },
              optionValue: "stationCode", // 接口返回stationNo字段
              optionLabel: "stationCode", // 显示stationNo字段
              filterable: true,
              placeholder: "请选择站点编号",
            },
            on: {
              change: this.handleStationCodeChange,
            },
            preview: this.isEdit,
          },
          {
            field: "stationName",
            title: "站点名称",
            element: "page-selector",
            rules: [{ required: true, message: "请选择站点名称" }],
            props: {
              fetchMethod: (params) => {
                return this.fetchStationData(params, "stationName");
              },
              optionValue: "stationName", // 显示的值
              optionLabel: "stationName", // 选中的值
              filterable: true,
              placeholder: "请选择站点名称",
            },
            on: {
              change: this.handleStationNameChange,
            },
            preview: this.isEdit,
          },
          {
            field: "orgNo",
            title: "所属区域",
            preview: true,
          },
          {
            field: "belongPlace",
            title: "省市",
            preview: true,
          },
          {
            field: "settleProgress",
            title: "结算进度",
            element: "el-select",
            props: {
              options: this.settleProgressOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [{ required: true, message: "请选择" }],
          },
          {
            field: "settlementType",
            title: "结算类型",
            element: "el-select",
            props: {
              options: this.settlementTypeOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
            },
            rules: [{ required: true, message: "请选择" }],
          },
          {
            field: "billDateRange",
            title: "账单周期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
            },
            rules: [{ required: true, message: "请选择" }],
          },
          {
            field: "serviceFeeAmount",
            title: "服务费应结算金额（元）",
            element: "el-input-number",
            props: {
              precision: 2,
            },
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "serviceFeeStandard",
            title: "服务费计算标准（%）",
            element: "el-input-number",
            props: {
              precision: 3,
            },
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "serviceFeeRealAmount",
            title: "服务费实际结算金额（元）",
            element: "el-input-number",
            props: {
              precision: 2,
            },
            rules: [{ required: true, message: "请输入" }],
          },
          {
            field: "serviceFeeSettleBy",
            title: "服务费结算人员",
            element: "el-autocomplete",
            props: {
              fetchSuggestions: (queryString, cb) => {
                return this.querySearch(queryString, cb, "settlePersonList");
              },
            },
          },
          {
            field: "settleRemark",
            title: "结算备注",
            props: {
              type: "textarea",
            },
            attrs: {
              rows: 5,
              maxlength: 500,
              showWordLimit: true,
              placeholder: "500个字符以内",
            },
          },
        ],
      };
      return {
        viewBtn: false,
        submitBtn: false,
        okText: "确认",
        cancelText: "取消",
        addBtn: false,
        editBtn: checkPermission(["serviceBill:serviceFeeBill:edit"]),
        delBtn: checkPermission(["serviceBill:serviceFeeBill:delete"]),
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "60%",
        formConfig: form[this.operationType],
        crudPermission: [],
        customOperationTypes: [
          {
            title: "日志",
            typeName: "log",
            slotName: "log",
            showForm: false,
            event: (row) => {
              return this.handleLog(row);
            },
            condition: (row) => {
              return checkPermission(["serviceBill:serviceFeeBill:log"]);
            },
          },
        ],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "220px",
        },
      };
    },
  },
};
</script>

<style></style>
